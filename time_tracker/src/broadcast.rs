use std::collections::HashMap;
use std::sync::{Arc, RwLock, Mutex};
use crossbeam_channel::{Sender, Receiver};
use tungstenite::{Message, WebSocket};
use std::net::TcpStream;
use log::{error, warn, debug};

use crate::structs::{TrackingStatus, WebSocketMessage};
use crate::web_socket::WebSocketBroadcastMessage;

/// Connection wrapper that tracks connection health
struct Connection {
    websocket: Arc<Mutex<WebSocket<TcpStream>>>,
    is_healthy: Arc<Mutex<bool>>,
}

impl Connection {
    fn new(websocket: WebSocket<TcpStream>) -> Self {
        Self {
            websocket: Arc::new(Mutex::new(websocket)),
            is_healthy: Arc::new(Mutex::new(true)),
        }
    }

    fn mark_unhealthy(&self) {
        if let Ok(mut healthy) = self.is_healthy.lock() {
            *healthy = false;
        }
    }

    fn is_healthy(&self) -> bool {
        self.is_healthy.lock().map(|h| *h).unwrap_or(false)
    }

    fn get_websocket(&self) -> Arc<Mutex<WebSocket<TcpStream>>> {
        Arc::clone(&self.websocket)
    }
}

/// Manages broadcasting messages to all connected WebSocket clients
pub struct Broadcaster {
    broadcast_sender: Sender<WebSocketBroadcastMessage>,
    broadcast_receiver: Arc<Mutex<Receiver<WebSocketBroadcastMessage>>>,
    connections: Arc<RwLock<HashMap<u64, Connection>>>,
}

impl Broadcaster {
    pub fn new() -> Self {
        let (broadcast_sender, broadcast_receiver) = crossbeam_channel::unbounded();
        Self {
            broadcast_sender,
            broadcast_receiver: Arc::new(Mutex::new(broadcast_receiver)),
            connections: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Add a new WebSocket connection to the broadcaster
    pub fn add_connection(&self, connection_id: u64, websocket: WebSocket<TcpStream>) {
        let connection = Connection::new(websocket);
        if let Ok(mut connections) = self.connections.write() {
            connections.insert(connection_id, connection);
            dbg!("Added connection {} to broadcaster", connection_id);
        } else {
            error!("Failed to acquire write lock for connections when adding connection {}", connection_id);
        }
    }

    /// Remove a WebSocket connection from the broadcaster
    pub fn remove_connection(&self, connection_id: u64) {
        if let Ok(mut connections) = self.connections.write() {
            connections.remove(&connection_id);
            dbg!("Removed connection {} from broadcaster", connection_id);
        } else {
            error!("Failed to acquire write lock for connections when removing connection {}", connection_id);
        }
    }

    /// Get a WebSocket connection by ID
    pub fn get_connection(&self, connection_id: u64) -> Option<Arc<Mutex<WebSocket<TcpStream>>>> {
        self.connections.read().ok()?.get(&connection_id).map(|conn| conn.get_websocket())
    }

    /// Broadcast a message to all connected WebSocket clients with resilient error handling
    pub fn broadcast_message(&self, message: &str) -> Result<(), Box<dyn std::error::Error>> {
        let connections = match self.connections.read() {
            Ok(conns) => conns,
            Err(e) => {
                error!("Failed to acquire read lock for connections: {}", e);
                return Err(format!("Lock error: {}", e).into());
            }
        };

        let mut failed_connections = Vec::new();
        let mut successful_sends = 0;
        let total_connections = connections.len();

        for (id, connection) in connections.iter() {
            if !connection.is_healthy() {
                failed_connections.push(*id);
                continue;
            }

            match connection.websocket.lock() {
                Ok(mut ws) => {
                    match ws.send(Message::text(message)) {
                        Ok(_) => {
                            successful_sends += 1;
                            dbg!("Successfully sent message to connection {}", id);
                        }
                        Err(e) => {
                            error!("Failed to send message to connection {}: {}", id, e);
                            connection.mark_unhealthy();
                            failed_connections.push(*id);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to acquire websocket lock for connection {}: {}", id, e);
                    connection.mark_unhealthy();
                    failed_connections.push(*id);
                }
            }
        }

        drop(connections);

        if !failed_connections.is_empty() {
            self.cleanup_failed_connections(failed_connections);
        }

        if total_connections > 0 {
            dbg!("Broadcast completed: {}/{} successful sends", successful_sends, total_connections);
        }

        Ok(())
    }

    /// Clean up failed connections in a separate method to avoid lock contention
    fn cleanup_failed_connections(&self, failed_ids: Vec<u64>) {
        match self.connections.write() {
            Ok(mut connections) => {
                for id in failed_ids {
                    connections.remove(&id);
                    dbg!("Cleaned up failed connection {}", id);
                }
            }
            Err(e) => {
                error!("Failed to acquire write lock for connection cleanup: {}", e);
            }
        }
    }

    /// Get the broadcast sender for sending messages
    pub fn get_sender(&self) -> Sender<WebSocketBroadcastMessage> {
        self.broadcast_sender.clone()
    }

    /// Get the broadcast receiver for processing messages
    pub fn get_receiver(&self) -> Arc<Mutex<Receiver<WebSocketBroadcastMessage>>> {
        Arc::clone(&self.broadcast_receiver)
    }

    /// Get connection count for monitoring
    pub fn connection_count(&self) -> usize {
        self.connections.read().map(|conns| conns.len()).unwrap_or(0)
    }

    /// Get healthy connection count
    pub fn healthy_connection_count(&self) -> usize {
        match self.connections.read() {
            Ok(connections) => connections.values().filter(|conn| conn.is_healthy()).count(),
            Err(_) => 0,
        }
    }
}

impl Default for Broadcaster {
    fn default() -> Self {
        Self::new()
    }
}