use std::collections::HashMap;
use std::sync::{<PERSON>, RwLock, Mutex};
use crossbeam_channel::{Sender, Receiver};
use tungstenite::{Message, WebSocket};
use std::net::TcpStream;
use log::error;

use crate::structs::{TrackingStatus, WebSocketMessage};
use crate::web_socket::WebSocketBroadcastMessage;

/// Manages broadcasting messages to all connected WebSocket clients
pub struct Broadcaster {
    // Channel for broadcasting messages to all WebSocket connections
    broadcast_sender: Sender<WebSocketBroadcastMessage>,
    broadcast_receiver: Receiver<WebSocketBroadcastMessage>,
    // Store active WebSocket connections
    connections: Arc<RwLock<HashMap<u64, Arc<Mutex<WebSocket<TcpStream>>>>>>,
}

impl Broadcaster {
    pub fn new() -> Self {
        let (broadcast_sender, broadcast_receiver) = crossbeam_channel::unbounded();
        Self {
            broadcast_sender,
            broadcast_receiver,
            connections: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Add a new WebSocket connection to the broadcaster
    pub fn add_connection(&self, connection_id: u64, websocket: WebSocket<TcpStream>) {
        let connection = Arc::new(Mutex::new(websocket));
        self.add_connection_with_id(connection_id, connection);
    }

    /// Remove a WebSocket connection from the broadcaster
    pub fn remove_connection(&self, connection_id: u64) {
        self.connections.write().unwrap().remove(&connection_id);
    }

    /// Add a WebSocket connection with a specific ID
    pub fn add_connection_with_id(&self, connection_id: u64, connection: Arc<Mutex<WebSocket<TcpStream>>>) {
        self.connections.write().unwrap().insert(connection_id, connection);
    }

    /// Get a WebSocket connection by ID
    pub fn get_connection(&self, connection_id: u64) -> Option<Arc<Mutex<WebSocket<TcpStream>>>> {
        self.connections.read().unwrap().get(&connection_id).cloned()
    }

    /// Broadcast a message to all connected WebSocket clients
    pub fn broadcast_message(&self, message: &str) -> Result<(), Box<dyn std::error::Error>> {
        let connections = self.connections.read().unwrap();
        let mut disconnected_connections = Vec::new();

        for (id, connection) in connections.iter() {
            match connection.lock() {
                Ok(mut ws) => {
                    if let Err(e) = ws.send(Message::text(message)) {
                        error!("Failed to send message to connection {}: {}", id, e);
                        disconnected_connections.push(*id);
                    }
                }
                Err(e) => {
                    error!("Failed to acquire lock for connection {}: {}", id, e);
                    disconnected_connections.push(*id);
                }
            }
        }

        // Remove disconnected connections
        drop(connections); // Release the read lock before acquiring a write lock
        if !disconnected_connections.is_empty() {
            let mut connections = self.connections.write().unwrap();
            for id in disconnected_connections {
                connections.remove(&id);
            }
        }

        Ok(())
    }

    /// Get the broadcast sender for sending messages
    pub fn get_sender(&self) -> Sender<WebSocketBroadcastMessage> {
        self.broadcast_sender.clone()
    }

    /// Get the broadcast receiver for processing messages
    pub fn get_receiver(&self) -> &Receiver<WebSocketBroadcastMessage> {
        &self.broadcast_receiver
    }
}

impl Default for Broadcaster {
    fn default() -> Self {
        Self::new()
    }
}