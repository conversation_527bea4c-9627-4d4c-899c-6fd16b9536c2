use crate::structs::TrackingStatus;
use crate::time_tracking::{PROCESS_MAP, PAUSE};
use chrono;

/// Compute the current tracking status based on the process map and pause state
pub fn compute_tracking_status() -> TrackingStatus {
    let process_map = PROCESS_MAP.lock().unwrap();

    // Check if any process is currently running and get session info
    let mut any_process_running = false;
    let mut current_running_process = None;
    let mut current_session_duration = 0;
    let mut session_start_time = None;

    for (process_name, process_info) in process_map.iter() {
        if process_info.is_running {
            any_process_running = true;
            current_running_process = Some(process_name.clone());
            current_session_duration = process_info.session_duration;
            if let Some(start) = process_info.session_start {
                // Convert std::time::Instant to chrono::DateTime<Utc>
                let std_duration = start.elapsed();
                let chrono_duration = chrono::Duration::from_std(std_duration).unwrap_or(chrono::Duration::zero());
                session_start_time = Some((chrono::Utc::now() - chrono_duration).to_rfc3339());
            }
            break;
        }
    }

    // Check if tracking is paused
    let is_paused = *PAUSE.read().unwrap();

    TrackingStatus {
        is_tracking: any_process_running && !is_paused,
        is_paused,
        current_app: current_running_process,
        current_session_duration,
        session_start_time,
        active_checkpoint_ids: vec![],
        current_checkpoint_id: None,
    }
}